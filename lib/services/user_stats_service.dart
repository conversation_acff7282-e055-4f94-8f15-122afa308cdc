import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class UserStatsService {
  static const String _statsKey = 'user_stats';
  
  // Get user stats
  Future<UserStats> getUserStats(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString('${_statsKey}_$userId');
    
    if (statsJson != null) {
      return UserStats.fromJson(jsonDecode(statsJson));
    } else {
      // Create default stats for new user
      final defaultStats = UserStats(
        userId: userId,
        totalCoins: 100, // Starting coins
        currentStreak: 0,
        longestStreak: 0,
        totalChallengesCompleted: 0,
        totalMeditationMinutes: 0,
        lastActivityDate: DateTime.now(),
        achievements: [],
        gameScores: {},
      );
      
      await updateUserStats(defaultStats);
      return defaultStats;
    }
  }

  // Update user stats
  Future<void> updateUserStats(UserStats stats) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('${_statsKey}_${stats.userId}', jsonEncode(stats.toJson()));
  }

  // Get all user stats (for leaderboards, etc.)
  Future<List<UserStats>> getAllUserStats() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where((key) => key.startsWith(_statsKey));
    
    final List<UserStats> allStats = [];
    for (final key in keys) {
      final statsJson = prefs.getString(key);
      if (statsJson != null) {
        allStats.add(UserStats.fromJson(jsonDecode(statsJson)));
      }
    }
    
    return allStats;
  }

  // Delete user stats
  Future<void> deleteUserStats(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('${_statsKey}_$userId');
  }

  // Get leaderboard by total coins
  Future<List<UserStats>> getCoinsLeaderboard({int limit = 10}) async {
    final allStats = await getAllUserStats();
    allStats.sort((a, b) => b.totalCoins.compareTo(a.totalCoins));
    return allStats.take(limit).toList();
  }

  // Get leaderboard by current streak
  Future<List<UserStats>> getStreakLeaderboard({int limit = 10}) async {
    final allStats = await getAllUserStats();
    allStats.sort((a, b) => b.currentStreak.compareTo(a.currentStreak));
    return allStats.take(limit).toList();
  }

  // Get leaderboard by challenges completed
  Future<List<UserStats>> getChallengesLeaderboard({int limit = 10}) async {
    final allStats = await getAllUserStats();
    allStats.sort((a, b) => b.totalChallengesCompleted.compareTo(a.totalChallengesCompleted));
    return allStats.take(limit).toList();
  }

  // Get leaderboard by meditation minutes
  Future<List<UserStats>> getMeditationLeaderboard({int limit = 10}) async {
    final allStats = await getAllUserStats();
    allStats.sort((a, b) => b.totalMeditationMinutes.compareTo(a.totalMeditationMinutes));
    return allStats.take(limit).toList();
  }
}
