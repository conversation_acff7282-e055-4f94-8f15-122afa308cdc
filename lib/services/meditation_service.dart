import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class MeditationService {
  static const String _meditationsKey = 'meditations';
  static const String _sessionsKey = 'meditation_sessions';
  
  // Mock meditation data
  final List<Map<String, dynamic>> _mockMeditations = [
    {
      'id': 'breathing_basic',
      'title': 'Basic Breathing',
      'description': 'Simple breathing exercise for beginners',
      'type': 'breathing',
      'duration': 5,
      'audioPath': 'audio/breathing_basic.mp3',
      'imagePath': null,
      'tags': ['beginner', 'breathing', 'relaxation'],
    },
    {
      'id': 'breathing_advanced',
      'title': 'Deep Breathing',
      'description': 'Advanced breathing techniques for stress relief',
      'type': 'breathing',
      'duration': 10,
      'audioPath': 'audio/breathing_advanced.mp3',
      'imagePath': null,
      'tags': ['advanced', 'breathing', 'stress-relief'],
    },
    {
      'id': 'mindfulness_present',
      'title': 'Present Moment',
      'description': 'Focus on the present moment and let go of worries',
      'type': 'mindfulness',
      'duration': 8,
      'audioPath': 'audio/mindfulness_present.mp3',
      'imagePath': null,
      'tags': ['mindfulness', 'present', 'awareness'],
    },
    {
      'id': 'mindfulness_body',
      'title': 'Body Scan',
      'description': 'Mindful body scan meditation',
      'type': 'mindfulness',
      'duration': 15,
      'audioPath': 'audio/mindfulness_body.mp3',
      'imagePath': null,
      'tags': ['mindfulness', 'body-scan', 'relaxation'],
    },
    {
      'id': 'relaxation_sleep',
      'title': 'Sleep Preparation',
      'description': 'Gentle relaxation to prepare for sleep',
      'type': 'relaxation',
      'duration': 12,
      'audioPath': 'audio/relaxation_sleep.mp3',
      'imagePath': null,
      'tags': ['relaxation', 'sleep', 'evening'],
    },
    {
      'id': 'relaxation_stress',
      'title': 'Stress Relief',
      'description': 'Quick stress relief meditation',
      'type': 'relaxation',
      'duration': 7,
      'audioPath': 'audio/relaxation_stress.mp3',
      'imagePath': null,
      'tags': ['relaxation', 'stress-relief', 'quick'],
    },
    {
      'id': 'focus_concentration',
      'title': 'Concentration Boost',
      'description': 'Improve focus and concentration',
      'type': 'focus',
      'duration': 10,
      'audioPath': 'audio/focus_concentration.mp3',
      'imagePath': null,
      'tags': ['focus', 'concentration', 'productivity'],
    },
    {
      'id': 'focus_clarity',
      'title': 'Mental Clarity',
      'description': 'Clear your mind and enhance mental clarity',
      'type': 'focus',
      'duration': 13,
      'audioPath': 'audio/focus_clarity.mp3',
      'imagePath': null,
      'tags': ['focus', 'clarity', 'mental'],
    },
  ];

  // Initialize mock data
  Future<void> _initializeMockData() async {
    final prefs = await SharedPreferences.getInstance();
    final existingMeditations = prefs.getString(_meditationsKey);
    
    if (existingMeditations == null) {
      await prefs.setString(_meditationsKey, jsonEncode(_mockMeditations));
    }
  }

  // Get all meditations
  Future<List<Meditation>> getAllMeditations() async {
    await _initializeMockData();
    final prefs = await SharedPreferences.getInstance();
    final meditationsJson = prefs.getString(_meditationsKey) ?? '[]';
    final meditationsData = List<Map<String, dynamic>>.from(jsonDecode(meditationsJson));
    
    return meditationsData.map((data) => Meditation.fromJson(data)).toList();
  }

  // Get meditation by ID
  Future<Meditation?> getMeditationById(String meditationId) async {
    final meditations = await getAllMeditations();
    try {
      return meditations.firstWhere((m) => m.id == meditationId);
    } catch (e) {
      return null;
    }
  }

  // Get meditations by type
  Future<List<Meditation>> getMeditationsByType(MeditationType type) async {
    final meditations = await getAllMeditations();
    return meditations.where((m) => m.type == type).toList();
  }

  // Get meditations by duration range
  Future<List<Meditation>> getMeditationsByDuration(int minMinutes, int maxMinutes) async {
    final meditations = await getAllMeditations();
    return meditations.where((m) => 
        m.duration >= minMinutes && m.duration <= maxMinutes).toList();
  }

  // Get meditations by tag
  Future<List<Meditation>> getMeditationsByTag(String tag) async {
    final meditations = await getAllMeditations();
    return meditations.where((m) => m.tags.contains(tag)).toList();
  }

  // Save meditation session
  Future<void> saveMeditationSession(MeditationSession session) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getString(_sessionsKey) ?? '[]';
    final sessions = List<Map<String, dynamic>>.from(jsonDecode(sessionsJson));
    
    sessions.add(session.toJson());
    await prefs.setString(_sessionsKey, jsonEncode(sessions));
  }

  // Get meditation sessions for user
  Future<List<MeditationSession>> getUserMeditationSessions(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getString(_sessionsKey) ?? '[]';
    final sessionsData = List<Map<String, dynamic>>.from(jsonDecode(sessionsJson));
    
    final userSessions = sessionsData.where((s) => s['userId'] == userId).toList();
    return userSessions.map((data) => MeditationSession.fromJson(data)).toList();
  }

  // Get completed sessions count for user
  Future<int> getCompletedSessionsCount(String userId) async {
    final sessions = await getUserMeditationSessions(userId);
    return sessions.where((s) => s.completed).length;
  }

  // Get total meditation time for user
  Future<int> getTotalMeditationTime(String userId) async {
    final sessions = await getUserMeditationSessions(userId);
    return sessions.fold(0, (total, session) => total + session.duration);
  }

  // Get meditation streak for user
  Future<int> getMeditationStreak(String userId) async {
    final sessions = await getUserMeditationSessions(userId);
    if (sessions.isEmpty) return 0;
    
    // Sort sessions by start time (most recent first)
    sessions.sort((a, b) => b.startTime.compareTo(a.startTime));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final session in sessions) {
      if (!session.completed) continue;
      
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      
      if (lastDate == null) {
        // First session
        lastDate = sessionDate;
        streak = 1;
      } else {
        final daysDifference = lastDate.difference(sessionDate).inDays;
        
        if (daysDifference == 1) {
          // Consecutive day
          streak++;
          lastDate = sessionDate;
        } else if (daysDifference > 1) {
          // Gap in streak
          break;
        }
        // If daysDifference == 0, same day - continue without incrementing
      }
    }
    
    return streak;
  }

  // Get favorite meditations for user (most used)
  Future<List<Meditation>> getFavoriteMeditations(String userId, {int limit = 5}) async {
    final sessions = await getUserMeditationSessions(userId);
    final meditations = await getAllMeditations();
    
    // Count sessions per meditation
    final Map<String, int> meditationCounts = {};
    for (final session in sessions) {
      if (session.completed) {
        meditationCounts[session.meditationId] = 
            (meditationCounts[session.meditationId] ?? 0) + 1;
      }
    }
    
    // Sort by count and get top meditations
    final sortedEntries = meditationCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final List<Meditation> favorites = [];
    for (final entry in sortedEntries.take(limit)) {
      final meditation = meditations.firstWhere(
        (m) => m.id == entry.key,
        orElse: () => meditations.first,
      );
      favorites.add(meditation);
    }
    
    return favorites;
  }

  // Delete all sessions for user (for testing/reset)
  Future<void> deleteUserSessions(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getString(_sessionsKey) ?? '[]';
    final sessions = List<Map<String, dynamic>>.from(jsonDecode(sessionsJson));
    
    sessions.removeWhere((s) => s['userId'] == userId);
    await prefs.setString(_sessionsKey, jsonEncode(sessions));
  }
}
