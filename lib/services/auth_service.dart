import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class AuthService {
  static const String _usersKey = 'users';
  
  // Mock users for testing
  final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': 'user_1',
      'username': 'testuser',
      'email': '<EMAIL>',
      'password': 'password123',
      'createdAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    },
    {
      'id': 'user_2',
      'username': 'demo',
      'email': '<EMAIL>',
      'password': 'demo123',
      'createdAt': DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      'lastLoginAt': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
    },
  ];

  // Initialize mock data
  Future<void> _initializeMockData() async {
    final prefs = await SharedPreferences.getInstance();
    final existingUsers = prefs.getString(_usersKey);
    
    if (existingUsers == null) {
      await prefs.setString(_usersKey, jsonEncode(_mockUsers));
    }
  }

  // Get all users from storage
  Future<List<Map<String, dynamic>>> _getUsers() async {
    await _initializeMockData();
    final prefs = await SharedPreferences.getInstance();
    final usersJson = prefs.getString(_usersKey) ?? '[]';
    return List<Map<String, dynamic>>.from(jsonDecode(usersJson));
  }

  // Save users to storage
  Future<void> _saveUsers(List<Map<String, dynamic>> users) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_usersKey, jsonEncode(users));
  }

  // Login user
  Future<User?> login(String email, String password) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    final users = await _getUsers();
    
    try {
      final userData = users.firstWhere(
        (user) => user['email'] == email && user['password'] == password,
      );
      
      // Update last login time
      userData['lastLoginAt'] = DateTime.now().toIso8601String();
      await _saveUsers(users);
      
      // Remove password from user data before creating User object
      final userDataCopy = Map<String, dynamic>.from(userData);
      userDataCopy.remove('password');
      
      return User.fromJson(userDataCopy);
    } catch (e) {
      return null; // User not found or wrong password
    }
  }

  // Register new user
  Future<User?> register(String username, String email, String password) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    final users = await _getUsers();
    
    // Check if email already exists
    final emailExists = users.any((user) => user['email'] == email);
    if (emailExists) {
      throw Exception('Email already exists');
    }
    
    // Check if username already exists
    final usernameExists = users.any((user) => user['username'] == username);
    if (usernameExists) {
      throw Exception('Username already exists');
    }
    
    // Create new user
    final newUser = {
      'id': 'user_${DateTime.now().millisecondsSinceEpoch}',
      'username': username,
      'email': email,
      'password': password,
      'createdAt': DateTime.now().toIso8601String(),
      'lastLoginAt': DateTime.now().toIso8601String(),
    };
    
    users.add(newUser);
    await _saveUsers(users);
    
    // Remove password from user data before creating User object
    final userDataCopy = Map<String, dynamic>.from(newUser);
    userDataCopy.remove('password');
    
    return User.fromJson(userDataCopy);
  }

  // Get user by ID
  Future<User?> getUserById(String userId) async {
    final users = await _getUsers();
    
    try {
      final userData = users.firstWhere((user) => user['id'] == userId);
      
      // Remove password from user data before creating User object
      final userDataCopy = Map<String, dynamic>.from(userData);
      userDataCopy.remove('password');
      
      return User.fromJson(userDataCopy);
    } catch (e) {
      return null;
    }
  }

  // Update user
  Future<User?> updateUser(User user) async {
    final users = await _getUsers();
    
    final index = users.indexWhere((u) => u['id'] == user.id);
    if (index == -1) return null;
    
    // Update user data (preserve password)
    final existingPassword = users[index]['password'];
    users[index] = user.toJson();
    users[index]['password'] = existingPassword;
    
    await _saveUsers(users);
    return user;
  }

  // Change password
  Future<bool> changePassword(String userId, String oldPassword, String newPassword) async {
    final users = await _getUsers();
    
    final index = users.indexWhere((u) => u['id'] == userId);
    if (index == -1) return false;
    
    // Verify old password
    if (users[index]['password'] != oldPassword) {
      return false;
    }
    
    // Update password
    users[index]['password'] = newPassword;
    await _saveUsers(users);
    
    return true;
  }

  // Delete user
  Future<bool> deleteUser(String userId) async {
    final users = await _getUsers();
    
    final index = users.indexWhere((u) => u['id'] == userId);
    if (index == -1) return false;
    
    users.removeAt(index);
    await _saveUsers(users);
    
    return true;
  }
}
