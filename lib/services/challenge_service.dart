import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class ChallengeService {
  static const String _challengesKey = 'challenges';
  static const String _resultsKey = 'challenge_results';
  
  // Mock challenges data
  final List<Map<String, dynamic>> _mockChallenges = [
    {
      'id': 'tap_timer_easy',
      'title': 'Speed Tapper',
      'description': 'Tap as fast as you can in 30 seconds!',
      'type': 'tapTimer',
      'difficulty': 'easy',
      'coinReward': 10,
      'timeLimit': 30,
      'gameConfig': {'targetTaps': 50},
    },
    {
      'id': 'tap_timer_medium',
      'title': 'Lightning Fingers',
      'description': 'Can you tap 100 times in 45 seconds?',
      'type': 'tapTimer',
      'difficulty': 'medium',
      'coinReward': 20,
      'timeLimit': 45,
      'gameConfig': {'targetTaps': 100},
    },
    {
      'id': 'memory_match_easy',
      'title': 'Memory Master',
      'description': 'Match the sequence of colors!',
      'type': 'memoryMatch',
      'difficulty': 'easy',
      'coinReward': 15,
      'timeLimit': 60,
      'gameConfig': {'sequenceLength': 4, 'colors': 4},
    },
    {
      'id': 'memory_match_medium',
      'title': 'Mind Palace',
      'description': 'Remember longer sequences!',
      'type': 'memoryMatch',
      'difficulty': 'medium',
      'coinReward': 25,
      'timeLimit': 90,
      'gameConfig': {'sequenceLength': 6, 'colors': 6},
    },
    {
      'id': 'memory_match_hard',
      'title': 'Memory Champion',
      'description': 'Ultimate memory challenge!',
      'type': 'memoryMatch',
      'difficulty': 'hard',
      'coinReward': 40,
      'timeLimit': 120,
      'gameConfig': {'sequenceLength': 8, 'colors': 8},
    },
    {
      'id': 'color_sequence_easy',
      'title': 'Color Flow',
      'description': 'Follow the color pattern!',
      'type': 'colorSequence',
      'difficulty': 'easy',
      'coinReward': 12,
      'timeLimit': 45,
      'gameConfig': {'patternLength': 5},
    },
    {
      'id': 'math_quiz_easy',
      'title': 'Quick Math',
      'description': 'Solve simple math problems!',
      'type': 'mathQuiz',
      'difficulty': 'easy',
      'coinReward': 18,
      'timeLimit': 60,
      'gameConfig': {'questionCount': 10, 'maxNumber': 20},
    },
  ];

  // Initialize mock data
  Future<void> _initializeMockData() async {
    final prefs = await SharedPreferences.getInstance();
    final existingChallenges = prefs.getString(_challengesKey);
    
    if (existingChallenges == null) {
      await prefs.setString(_challengesKey, jsonEncode(_mockChallenges));
    }
  }

  // Get all challenges
  Future<List<Challenge>> getAllChallenges() async {
    await _initializeMockData();
    final prefs = await SharedPreferences.getInstance();
    final challengesJson = prefs.getString(_challengesKey) ?? '[]';
    final challengesData = List<Map<String, dynamic>>.from(jsonDecode(challengesJson));
    
    return challengesData.map((data) => Challenge.fromJson(data)).toList();
  }

  // Get challenge by ID
  Future<Challenge?> getChallengeById(String challengeId) async {
    final challenges = await getAllChallenges();
    try {
      return challenges.firstWhere((c) => c.id == challengeId);
    } catch (e) {
      return null;
    }
  }

  // Get challenges by type
  Future<List<Challenge>> getChallengesByType(ChallengeType type) async {
    final challenges = await getAllChallenges();
    return challenges.where((c) => c.type == type).toList();
  }

  // Get challenges by difficulty
  Future<List<Challenge>> getChallengesByDifficulty(ChallengeDifficulty difficulty) async {
    final challenges = await getAllChallenges();
    return challenges.where((c) => c.difficulty == difficulty).toList();
  }

  // Save challenge result
  Future<void> saveChallengeResult(ChallengeResult result) async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_resultsKey) ?? '[]';
    final results = List<Map<String, dynamic>>.from(jsonDecode(resultsJson));
    
    results.add(result.toJson());
    await prefs.setString(_resultsKey, jsonEncode(results));
  }

  // Get challenge results for user
  Future<List<ChallengeResult>> getUserChallengeResults(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_resultsKey) ?? '[]';
    final resultsData = List<Map<String, dynamic>>.from(jsonDecode(resultsJson));
    
    final userResults = resultsData.where((r) => r['userId'] == userId).toList();
    return userResults.map((data) => ChallengeResult.fromJson(data)).toList();
  }

  // Get best score for user and challenge
  Future<int> getBestScore(String userId, String challengeId) async {
    final results = await getUserChallengeResults(userId);
    final challengeResults = results.where((r) => r.challengeId == challengeId).toList();
    
    if (challengeResults.isEmpty) return 0;
    
    challengeResults.sort((a, b) => b.score.compareTo(a.score));
    return challengeResults.first.score;
  }

  // Get completion count for user and challenge
  Future<int> getCompletionCount(String userId, String challengeId) async {
    final results = await getUserChallengeResults(userId);
    return results.where((r) => r.challengeId == challengeId && r.completed).length;
  }

  // Get leaderboard for challenge
  Future<List<ChallengeResult>> getChallengeLeaderboard(String challengeId, {int limit = 10}) async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_resultsKey) ?? '[]';
    final resultsData = List<Map<String, dynamic>>.from(jsonDecode(resultsJson));
    
    final challengeResults = resultsData
        .where((r) => r['challengeId'] == challengeId && r['completed'] == true)
        .map((data) => ChallengeResult.fromJson(data))
        .toList();
    
    // Group by user and get best score for each user
    final Map<String, ChallengeResult> bestScores = {};
    for (final result in challengeResults) {
      if (!bestScores.containsKey(result.userId) || 
          bestScores[result.userId]!.score < result.score) {
        bestScores[result.userId] = result;
      }
    }
    
    final leaderboard = bestScores.values.toList();
    leaderboard.sort((a, b) => b.score.compareTo(a.score));
    
    return leaderboard.take(limit).toList();
  }

  // Delete all results for user (for testing/reset)
  Future<void> deleteUserResults(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_resultsKey) ?? '[]';
    final results = List<Map<String, dynamic>>.from(jsonDecode(resultsJson));
    
    results.removeWhere((r) => r['userId'] == userId);
    await prefs.setString(_resultsKey, jsonEncode(results));
  }
}
