import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class ShopService {
  static const String _shopItemsKey = 'shop_items';
  static const String _purchasedItemsKey = 'purchased_items';
  
  // Mock shop items data
  final List<Map<String, dynamic>> _mockShopItems = [
    // Avatars
    {
      'id': 'avatar_ninja',
      'name': 'Ninja Avatar',
      'description': 'Stealthy and focused ninja character',
      'type': 'avatar',
      'rarity': 'common',
      'price': 50,
      'imagePath': 'images/avatars/ninja.png',
      'properties': {'color': 'black', 'style': 'ninja'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'avatar_monk',
      'name': 'Zen Monk',
      'description': 'Peaceful and wise monk character',
      'type': 'avatar',
      'rarity': 'rare',
      'price': 100,
      'imagePath': 'images/avatars/monk.png',
      'properties': {'color': 'orange', 'style': 'monk'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'avatar_warrior',
      'name': 'Mind Warrior',
      'description': 'Strong and determined warrior',
      'type': 'avatar',
      'rarity': 'epic',
      'price': 200,
      'imagePath': 'images/avatars/warrior.png',
      'properties': {'color': 'gold', 'style': 'warrior'},
      'isLimited': true,
      'availableUntil': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
    },
    
    // Themes
    {
      'id': 'theme_ocean',
      'name': 'Ocean Breeze',
      'description': 'Calming ocean-themed interface',
      'type': 'theme',
      'rarity': 'common',
      'price': 75,
      'imagePath': 'images/themes/ocean.png',
      'properties': {'primaryColor': '#0077be', 'secondaryColor': '#87ceeb'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'theme_forest',
      'name': 'Forest Zen',
      'description': 'Nature-inspired green theme',
      'type': 'theme',
      'rarity': 'common',
      'price': 75,
      'imagePath': 'images/themes/forest.png',
      'properties': {'primaryColor': '#228b22', 'secondaryColor': '#90ee90'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'theme_sunset',
      'name': 'Sunset Glow',
      'description': 'Warm sunset colors',
      'type': 'theme',
      'rarity': 'rare',
      'price': 125,
      'imagePath': 'images/themes/sunset.png',
      'properties': {'primaryColor': '#ff6347', 'secondaryColor': '#ffd700'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'theme_galaxy',
      'name': 'Galaxy Dreams',
      'description': 'Cosmic space theme',
      'type': 'theme',
      'rarity': 'legendary',
      'price': 300,
      'imagePath': 'images/themes/galaxy.png',
      'properties': {'primaryColor': '#4b0082', 'secondaryColor': '#9370db'},
      'isLimited': true,
      'availableUntil': DateTime.now().add(const Duration(days: 14)).toIso8601String(),
    },
    
    // Power-ups
    {
      'id': 'powerup_double_coins',
      'name': 'Double Coins',
      'description': 'Double coin rewards for 24 hours',
      'type': 'powerup',
      'rarity': 'common',
      'price': 30,
      'imagePath': 'images/powerups/double_coins.png',
      'properties': {'duration': 24, 'multiplier': 2},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'powerup_time_boost',
      'name': 'Time Boost',
      'description': 'Extra 10 seconds for challenges',
      'type': 'powerup',
      'rarity': 'rare',
      'price': 60,
      'imagePath': 'images/powerups/time_boost.png',
      'properties': {'extraTime': 10, 'uses': 5},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'powerup_streak_saver',
      'name': 'Streak Saver',
      'description': 'Protects your streak for one missed day',
      'type': 'powerup',
      'rarity': 'epic',
      'price': 150,
      'imagePath': 'images/powerups/streak_saver.png',
      'properties': {'protection': 1},
      'isLimited': false,
      'availableUntil': null,
    },
    
    // Decorations
    {
      'id': 'decoration_lotus',
      'name': 'Lotus Flower',
      'description': 'Beautiful lotus decoration for your profile',
      'type': 'decoration',
      'rarity': 'common',
      'price': 25,
      'imagePath': 'images/decorations/lotus.png',
      'properties': {'size': 'small', 'animation': 'gentle'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'decoration_mandala',
      'name': 'Sacred Mandala',
      'description': 'Intricate mandala pattern',
      'type': 'decoration',
      'rarity': 'rare',
      'price': 80,
      'imagePath': 'images/decorations/mandala.png',
      'properties': {'size': 'medium', 'animation': 'rotating'},
      'isLimited': false,
      'availableUntil': null,
    },
    {
      'id': 'decoration_zen_garden',
      'name': 'Zen Garden',
      'description': 'Miniature zen garden with animated sand',
      'type': 'decoration',
      'rarity': 'legendary',
      'price': 250,
      'imagePath': 'images/decorations/zen_garden.png',
      'properties': {'size': 'large', 'animation': 'flowing'},
      'isLimited': true,
      'availableUntil': DateTime.now().add(const Duration(days: 7)).toIso8601String(),
    },
  ];

  // Initialize mock data
  Future<void> _initializeMockData() async {
    final prefs = await SharedPreferences.getInstance();
    final existingItems = prefs.getString(_shopItemsKey);
    
    if (existingItems == null) {
      await prefs.setString(_shopItemsKey, jsonEncode(_mockShopItems));
    }
  }

  // Get all shop items
  Future<List<ShopItem>> getAllShopItems() async {
    await _initializeMockData();
    final prefs = await SharedPreferences.getInstance();
    final itemsJson = prefs.getString(_shopItemsKey) ?? '[]';
    final itemsData = List<Map<String, dynamic>>.from(jsonDecode(itemsJson));
    
    return itemsData.map((data) => ShopItem.fromJson(data)).toList();
  }

  // Get shop item by ID
  Future<ShopItem?> getShopItemById(String itemId) async {
    final items = await getAllShopItems();
    try {
      return items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  // Get items by type
  Future<List<ShopItem>> getItemsByType(ShopItemType type) async {
    final items = await getAllShopItems();
    return items.where((item) => item.type == type).toList();
  }

  // Get items by rarity
  Future<List<ShopItem>> getItemsByRarity(ShopItemRarity rarity) async {
    final items = await getAllShopItems();
    return items.where((item) => item.rarity == rarity).toList();
  }

  // Get available items (not expired)
  Future<List<ShopItem>> getAvailableItems() async {
    final items = await getAllShopItems();
    final now = DateTime.now();
    
    return items.where((item) {
      if (item.isLimited && item.availableUntil != null) {
        return now.isBefore(item.availableUntil!);
      }
      return true;
    }).toList();
  }

  // Save purchased item
  Future<void> savePurchasedItem(PurchasedItem purchasedItem) async {
    final prefs = await SharedPreferences.getInstance();
    final purchasedJson = prefs.getString(_purchasedItemsKey) ?? '[]';
    final purchased = List<Map<String, dynamic>>.from(jsonDecode(purchasedJson));
    
    purchased.add(purchasedItem.toJson());
    await prefs.setString(_purchasedItemsKey, jsonEncode(purchased));
  }

  // Get purchased items for user
  Future<List<PurchasedItem>> getPurchasedItems(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final purchasedJson = prefs.getString(_purchasedItemsKey) ?? '[]';
    final purchasedData = List<Map<String, dynamic>>.from(jsonDecode(purchasedJson));
    
    final userPurchased = purchasedData.where((p) => p['userId'] == userId).toList();
    return userPurchased.map((data) => PurchasedItem.fromJson(data)).toList();
  }

  // Update purchased item
  Future<void> updatePurchasedItem(PurchasedItem purchasedItem) async {
    final prefs = await SharedPreferences.getInstance();
    final purchasedJson = prefs.getString(_purchasedItemsKey) ?? '[]';
    final purchased = List<Map<String, dynamic>>.from(jsonDecode(purchasedJson));
    
    final index = purchased.indexWhere((p) => p['id'] == purchasedItem.id);
    if (index != -1) {
      purchased[index] = purchasedItem.toJson();
      await prefs.setString(_purchasedItemsKey, jsonEncode(purchased));
    }
  }

  // Check if item is purchased by user
  Future<bool> isItemPurchased(String userId, String itemId) async {
    final purchasedItems = await getPurchasedItems(userId);
    return purchasedItems.any((item) => item.itemId == itemId);
  }

  // Get active items for user
  Future<List<PurchasedItem>> getActiveItems(String userId) async {
    final purchasedItems = await getPurchasedItems(userId);
    return purchasedItems.where((item) => item.isActive).toList();
  }

  // Delete purchased item
  Future<void> deletePurchasedItem(String purchasedItemId) async {
    final prefs = await SharedPreferences.getInstance();
    final purchasedJson = prefs.getString(_purchasedItemsKey) ?? '[]';
    final purchased = List<Map<String, dynamic>>.from(jsonDecode(purchasedJson));
    
    purchased.removeWhere((p) => p['id'] == purchasedItemId);
    await prefs.setString(_purchasedItemsKey, jsonEncode(purchased));
  }

  // Delete all purchased items for user (for testing/reset)
  Future<void> deleteUserPurchases(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final purchasedJson = prefs.getString(_purchasedItemsKey) ?? '[]';
    final purchased = List<Map<String, dynamic>>.from(jsonDecode(purchasedJson));
    
    purchased.removeWhere((p) => p['userId'] == userId);
    await prefs.setString(_purchasedItemsKey, jsonEncode(purchased));
  }
}
