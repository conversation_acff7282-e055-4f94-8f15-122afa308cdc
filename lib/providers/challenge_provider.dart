import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/challenge_service.dart';

class ChallengeProvider with ChangeNotifier {
  List<Challenge> _challenges = [];
  Challenge? _currentChallenge;
  ChallengeResult? _currentResult;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isGameActive = false;
  int _currentScore = 0;
  int _timeRemaining = 0;

  final ChallengeService _challengeService = ChallengeService();

  // Getters
  List<Challenge> get challenges => _challenges;
  Challenge? get currentChallenge => _currentChallenge;
  ChallengeResult? get currentResult => _currentResult;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isGameActive => _isGameActive;
  int get currentScore => _currentScore;
  int get timeRemaining => _timeRemaining;

  // Load all challenges
  Future<void> loadChallenges() async {
    _setLoading(true);
    try {
      _challenges = await _challengeService.getAllChallenges();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load challenges: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get daily challenge
  Challenge? getDailyChallenge() {
    if (_challenges.isEmpty) return null;
    
    // Simple logic: rotate through challenges based on day of year
    final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year)).inDays;
    return _challenges[dayOfYear % _challenges.length];
  }

  // Start challenge
  Future<void> startChallenge(String challengeId) async {
    try {
      _currentChallenge = _challenges.firstWhere((c) => c.id == challengeId);
      _currentScore = 0;
      _timeRemaining = _currentChallenge!.timeLimit;
      _isGameActive = true;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to start challenge: $e');
    }
  }

  // Update score during game
  void updateScore(int newScore) {
    if (_isGameActive) {
      _currentScore = newScore;
      notifyListeners();
    }
  }

  // Update time remaining
  void updateTimeRemaining(int seconds) {
    if (_isGameActive) {
      _timeRemaining = seconds;
      if (_timeRemaining <= 0) {
        endChallenge();
      }
      notifyListeners();
    }
  }

  // End challenge
  Future<void> endChallenge() async {
    if (_currentChallenge == null) return;

    try {
      _isGameActive = false;
      
      final timeSpent = _currentChallenge!.timeLimit - _timeRemaining;
      final completed = _currentScore > 0; // Simple completion logic
      final coinsEarned = completed ? _currentChallenge!.coinReward : 0;

      _currentResult = ChallengeResult(
        challengeId: _currentChallenge!.id,
        userId: 'current_user', // This should come from auth provider
        score: _currentScore,
        timeSpent: timeSpent,
        completed: completed,
        completedAt: DateTime.now(),
        coinsEarned: coinsEarned,
      );

      await _challengeService.saveChallengeResult(_currentResult!);
      notifyListeners();
    } catch (e) {
      _setError('Failed to end challenge: $e');
    }
  }

  // Reset current challenge
  void resetChallenge() {
    _currentChallenge = null;
    _currentResult = null;
    _currentScore = 0;
    _timeRemaining = 0;
    _isGameActive = false;
    _clearError();
    notifyListeners();
  }

  // Get challenge by type
  List<Challenge> getChallengesByType(ChallengeType type) {
    return _challenges.where((c) => c.type == type).toList();
  }

  // Get challenge by difficulty
  List<Challenge> getChallengesByDifficulty(ChallengeDifficulty difficulty) {
    return _challenges.where((c) => c.difficulty == difficulty).toList();
  }

  // Game-specific methods for tap timer
  void incrementTapScore() {
    if (_isGameActive && _currentChallenge?.type == ChallengeType.tapTimer) {
      _currentScore++;
      notifyListeners();
    }
  }

  // Game-specific methods for memory match
  void recordMemoryMatch(bool isCorrect) {
    if (_isGameActive && _currentChallenge?.type == ChallengeType.memoryMatch) {
      if (isCorrect) {
        _currentScore += 10;
      } else {
        _currentScore = _currentScore > 5 ? _currentScore - 5 : 0;
      }
      notifyListeners();
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
