import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/models.dart';
import '../services/meditation_service.dart';

class MeditationProvider with ChangeNotifier {
  List<Meditation> _meditations = [];
  Meditation? _currentMeditation;
  MeditationSession? _currentSession;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Audio player state
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isPaused = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  final MeditationService _meditationService = MeditationService();

  // Getters
  List<Meditation> get meditations => _meditations;
  Meditation? get currentMeditation => _currentMeditation;
  MeditationSession? get currentSession => _currentSession;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  AudioPlayer get audioPlayer => _audioPlayer;

  MeditationProvider() {
    _initializeAudioPlayer();
  }

  void _initializeAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      _isPlaying = state == PlayerState.playing;
      _isPaused = state == PlayerState.paused;
      notifyListeners();
    });

    _audioPlayer.onPositionChanged.listen((Duration position) {
      _currentPosition = position;
      notifyListeners();
    });

    _audioPlayer.onDurationChanged.listen((Duration duration) {
      _totalDuration = duration;
      notifyListeners();
    });
  }

  // Load all meditations
  Future<void> loadMeditations() async {
    _setLoading(true);
    try {
      _meditations = await _meditationService.getAllMeditations();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load meditations: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Start meditation session
  Future<void> startMeditation(String meditationId) async {
    try {
      _currentMeditation = _meditations.firstWhere((m) => m.id == meditationId);
      
      _currentSession = MeditationSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        meditationId: meditationId,
        userId: 'current_user', // This should come from auth provider
        startTime: DateTime.now(),
        duration: 0,
        completed: false,
      );

      // Load and play audio
      await _audioPlayer.setSource(AssetSource(_currentMeditation!.audioPath));
      await _audioPlayer.resume();
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to start meditation: $e');
    }
  }

  // Pause meditation
  Future<void> pauseMeditation() async {
    try {
      await _audioPlayer.pause();
      notifyListeners();
    } catch (e) {
      _setError('Failed to pause meditation: $e');
    }
  }

  // Resume meditation
  Future<void> resumeMeditation() async {
    try {
      await _audioPlayer.resume();
      notifyListeners();
    } catch (e) {
      _setError('Failed to resume meditation: $e');
    }
  }

  // Stop meditation
  Future<void> stopMeditation() async {
    try {
      await _audioPlayer.stop();
      
      if (_currentSession != null) {
        final endTime = DateTime.now();
        final duration = endTime.difference(_currentSession!.startTime).inSeconds;
        
        _currentSession = MeditationSession(
          id: _currentSession!.id,
          meditationId: _currentSession!.meditationId,
          userId: _currentSession!.userId,
          startTime: _currentSession!.startTime,
          endTime: endTime,
          duration: duration,
          completed: duration >= 60, // Consider completed if at least 1 minute
        );

        await _meditationService.saveMeditationSession(_currentSession!);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop meditation: $e');
    }
  }

  // End meditation session
  Future<void> endMeditation() async {
    await stopMeditation();
    _currentMeditation = null;
    _currentSession = null;
    _currentPosition = Duration.zero;
    _totalDuration = Duration.zero;
    _clearError();
    notifyListeners();
  }

  // Seek to position
  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
      notifyListeners();
    } catch (e) {
      _setError('Failed to seek: $e');
    }
  }

  // Get meditations by type
  List<Meditation> getMeditationsByType(MeditationType type) {
    return _meditations.where((m) => m.type == type).toList();
  }

  // Get meditation progress percentage
  double get progressPercentage {
    if (_totalDuration.inMilliseconds == 0) return 0.0;
    return _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
  }

  // Get remaining time
  Duration get remainingTime {
    return _totalDuration - _currentPosition;
  }

  // Format duration for display
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
