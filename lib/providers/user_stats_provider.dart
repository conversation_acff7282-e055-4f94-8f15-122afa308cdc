import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/user_stats_service.dart';

class UserStatsProvider with ChangeNotifier {
  UserStats? _userStats;
  bool _isLoading = false;
  String? _errorMessage;

  final UserStatsService _statsService = UserStatsService();

  // Getters
  UserStats? get userStats => _userStats;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Initialize user stats
  Future<void> initializeStats(String userId) async {
    _setLoading(true);
    try {
      _userStats = await _statsService.getUserStats(userId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load user stats: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add coins
  Future<void> addCoins(int amount) async {
    if (_userStats == null) return;

    try {
      _userStats = _userStats!.copyWith(
        totalCoins: _userStats!.totalCoins + amount,
      );
      await _statsService.updateUserStats(_userStats!);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add coins: $e');
    }
  }

  // Spend coins
  Future<bool> spendCoins(int amount) async {
    if (_userStats == null || _userStats!.totalCoins < amount) {
      return false;
    }

    try {
      _userStats = _userStats!.copyWith(
        totalCoins: _userStats!.totalCoins - amount,
      );
      await _statsService.updateUserStats(_userStats!);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to spend coins: $e');
      return false;
    }
  }

  // Update streak
  Future<void> updateStreak() async {
    if (_userStats == null) return;

    try {
      final now = DateTime.now();
      final lastActivity = _userStats!.lastActivityDate;
      final daysDifference = now.difference(lastActivity).inDays;

      int newStreak = _userStats!.currentStreak;
      
      if (daysDifference == 1) {
        // Continue streak
        newStreak += 1;
      } else if (daysDifference > 1) {
        // Reset streak
        newStreak = 1;
      }
      // If daysDifference == 0, keep current streak

      _userStats = _userStats!.copyWith(
        currentStreak: newStreak,
        longestStreak: newStreak > _userStats!.longestStreak 
            ? newStreak 
            : _userStats!.longestStreak,
        lastActivityDate: now,
      );

      await _statsService.updateUserStats(_userStats!);
      notifyListeners();
    } catch (e) {
      _setError('Failed to update streak: $e');
    }
  }

  // Complete challenge
  Future<void> completeChallenge(int score, String gameType, int coinsEarned) async {
    if (_userStats == null) return;

    try {
      final updatedGameScores = Map<String, int>.from(_userStats!.gameScores);
      if (!updatedGameScores.containsKey(gameType) || 
          updatedGameScores[gameType]! < score) {
        updatedGameScores[gameType] = score;
      }

      _userStats = _userStats!.copyWith(
        totalChallengesCompleted: _userStats!.totalChallengesCompleted + 1,
        totalCoins: _userStats!.totalCoins + coinsEarned,
        gameScores: updatedGameScores,
      );

      await _statsService.updateUserStats(_userStats!);
      await updateStreak();
    } catch (e) {
      _setError('Failed to complete challenge: $e');
    }
  }

  // Complete meditation
  Future<void> completeMeditation(int minutes) async {
    if (_userStats == null) return;

    try {
      _userStats = _userStats!.copyWith(
        totalMeditationMinutes: _userStats!.totalMeditationMinutes + minutes,
      );

      await _statsService.updateUserStats(_userStats!);
      await updateStreak();
    } catch (e) {
      _setError('Failed to complete meditation: $e');
    }
  }

  // Add achievement
  Future<void> addAchievement(String achievementId) async {
    if (_userStats == null || _userStats!.achievements.contains(achievementId)) {
      return;
    }

    try {
      final updatedAchievements = List<String>.from(_userStats!.achievements);
      updatedAchievements.add(achievementId);

      _userStats = _userStats!.copyWith(
        achievements: updatedAchievements,
      );

      await _statsService.updateUserStats(_userStats!);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add achievement: $e');
    }
  }

  // Get best score for game type
  int getBestScore(String gameType) {
    return _userStats?.gameScores[gameType] ?? 0;
  }

  // Check if user has achievement
  bool hasAchievement(String achievementId) {
    return _userStats?.achievements.contains(achievementId) ?? false;
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
