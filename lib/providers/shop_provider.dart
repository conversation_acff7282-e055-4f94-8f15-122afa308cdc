import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/shop_service.dart';

class ShopProvider with ChangeNotifier {
  List<ShopItem> _shopItems = [];
  List<PurchasedItem> _purchasedItems = [];
  bool _isLoading = false;
  String? _errorMessage;

  final ShopService _shopService = ShopService();

  // Getters
  List<ShopItem> get shopItems => _shopItems;
  List<PurchasedItem> get purchasedItems => _purchasedItems;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Load shop items
  Future<void> loadShopItems() async {
    _setLoading(true);
    try {
      _shopItems = await _shopService.getAllShopItems();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load shop items: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load purchased items for user
  Future<void> loadPurchasedItems(String userId) async {
    _setLoading(true);
    try {
      _purchasedItems = await _shopService.getPurchasedItems(userId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load purchased items: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Purchase item
  Future<bool> purchaseItem(String itemId, String userId, int userCoins) async {
    try {
      final item = _shopItems.firstWhere((item) => item.id == itemId);
      
      // Check if user has enough coins
      if (userCoins < item.price) {
        _setError('Insufficient coins');
        return false;
      }

      // Check if item is already purchased
      if (isItemPurchased(itemId)) {
        _setError('Item already purchased');
        return false;
      }

      // Check if limited item is still available
      if (item.isLimited && item.availableUntil != null) {
        if (DateTime.now().isAfter(item.availableUntil!)) {
          _setError('Item no longer available');
          return false;
        }
      }

      // Create purchased item
      final purchasedItem = PurchasedItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        itemId: itemId,
        userId: userId,
        purchasedAt: DateTime.now(),
        pricePaid: item.price,
        isActive: false,
      );

      // Save purchase
      await _shopService.savePurchasedItem(purchasedItem);
      _purchasedItems.add(purchasedItem);
      
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to purchase item: $e');
      return false;
    }
  }

  // Check if item is purchased
  bool isItemPurchased(String itemId) {
    return _purchasedItems.any((item) => item.itemId == itemId);
  }

  // Activate/deactivate item
  Future<void> toggleItemActive(String purchasedItemId) async {
    try {
      final index = _purchasedItems.indexWhere((item) => item.id == purchasedItemId);
      if (index == -1) return;

      final item = _purchasedItems[index];
      final updatedItem = PurchasedItem(
        id: item.id,
        itemId: item.itemId,
        userId: item.userId,
        purchasedAt: item.purchasedAt,
        pricePaid: item.pricePaid,
        isActive: !item.isActive,
      );

      await _shopService.updatePurchasedItem(updatedItem);
      _purchasedItems[index] = updatedItem;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to toggle item: $e');
    }
  }

  // Get items by type
  List<ShopItem> getItemsByType(ShopItemType type) {
    return _shopItems.where((item) => item.type == type).toList();
  }

  // Get items by rarity
  List<ShopItem> getItemsByRarity(ShopItemRarity rarity) {
    return _shopItems.where((item) => item.rarity == rarity).toList();
  }

  // Get available items (not purchased and not expired)
  List<ShopItem> getAvailableItems() {
    return _shopItems.where((item) {
      // Check if not purchased
      if (isItemPurchased(item.id)) return false;
      
      // Check if not expired (for limited items)
      if (item.isLimited && item.availableUntil != null) {
        if (DateTime.now().isAfter(item.availableUntil!)) return false;
      }
      
      return true;
    }).toList();
  }

  // Get purchased items by type
  List<PurchasedItem> getPurchasedItemsByType(ShopItemType type) {
    return _purchasedItems.where((purchasedItem) {
      final shopItem = _shopItems.firstWhere(
        (item) => item.id == purchasedItem.itemId,
        orElse: () => ShopItem(
          id: '',
          name: '',
          description: '',
          type: type,
          rarity: ShopItemRarity.common,
          price: 0,
        ),
      );
      return shopItem.type == type;
    }).toList();
  }

  // Get active items
  List<PurchasedItem> getActiveItems() {
    return _purchasedItems.where((item) => item.isActive).toList();
  }

  // Get shop item by id
  ShopItem? getShopItemById(String itemId) {
    try {
      return _shopItems.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  // Get total spent coins
  int getTotalSpentCoins() {
    return _purchasedItems.fold(0, (total, item) => total + item.pricePaid);
  }

  // Get purchase count
  int getPurchaseCount() {
    return _purchasedItems.length;
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
