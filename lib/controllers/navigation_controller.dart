import 'package:get/get.dart';
import 'app_routes.dart';

class NavigationController extends GetxController {
  // Navigation methods
  
  // Authentication navigation
  void goToLogin() {
    Get.offAllNamed(AppRoutes.login);
  }
  
  void goToRegister() {
    Get.toNamed(AppRoutes.register);
  }
  
  void goToDashboard() {
    Get.offAllNamed(AppRoutes.dashboard);
  }
  
  // Challenge navigation
  void goToChallenge() {
    Get.toNamed(AppRoutes.challenge);
  }
  
  void goToChallengeGame(String challengeId) {
    Get.toNamed(AppRoutes.challengeGame, arguments: {'challengeId': challengeId});
  }
  
  void goToChallengeResult() {
    Get.offNamed(AppRoutes.challengeResult);
  }
  
  // Meditation navigation
  void goToMeditation() {
    Get.toNamed(AppRoutes.meditation);
  }
  
  void goToMeditationPlayer(String meditationId) {
    Get.toNamed(AppRoutes.meditationPlayer, arguments: {'meditationId': meditationId});
  }
  
  // Profile navigation
  void goToProfile() {
    Get.toNamed(AppRoutes.profile);
  }
  
  // Shop navigation
  void goToShop() {
    Get.toNamed(AppRoutes.shop);
  }
  
  // Settings navigation
  void goToSettings() {
    Get.toNamed(AppRoutes.settings);
  }
  
  // Back navigation
  void goBack() {
    if (Get.canPop()) {
      Get.back();
    } else {
      goToDashboard();
    }
  }
  
  // Logout and go to login
  void logout() {
    Get.offAllNamed(AppRoutes.login);
  }
  
  // Show snackbar messages
  void showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.primaryColor,
      colorText: Get.theme.colorScheme.onPrimary,
      duration: const Duration(seconds: 2),
    );
  }
  
  void showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.error,
      colorText: Get.theme.colorScheme.onError,
      duration: const Duration(seconds: 3),
    );
  }
  
  void showInfo(String message) {
    Get.snackbar(
      'Info',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.secondary,
      colorText: Get.theme.colorScheme.onSecondary,
      duration: const Duration(seconds: 2),
    );
  }
  
  // Show loading dialog
  void showLoading([String? message]) {
    Get.dialog(
      AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message ?? 'Loading...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }
  
  // Hide loading dialog
  void hideLoading() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }
  
  // Show confirmation dialog
  Future<bool> showConfirmation(String title, String message) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
