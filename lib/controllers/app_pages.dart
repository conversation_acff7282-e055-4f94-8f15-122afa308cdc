import 'package:get/get.dart';
import '../screens/screens.dart';
import 'app_routes.dart';

class AppPages {
  static final List<GetPage> pages = [
    // Splash Screen
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashScreen(),
    ),
    
    // Authentication
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.register,
      page: () => const RegisterScreen(),
      transition: Transition.rightToLeft,
    ),
    
    // Main Dashboard
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardScreen(),
      transition: Transition.fadeIn,
    ),
    
    // Challenge Flow
    GetPage(
      name: AppRoutes.challenge,
      page: () => const ChallengeScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.challengeGame,
      page: () => const ChallengeGameScreen(),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: AppRoutes.challengeResult,
      page: () => const ChallengeResultScreen(),
      transition: Transition.fadeIn,
    ),
    
    // Meditation Flow
    GetPage(
      name: AppRoutes.meditation,
      page: () => const MeditationScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.meditationPlayer,
      page: () => const MeditationPlayerScreen(),
      transition: Transition.downToUp,
    ),
    
    // Profile
    GetPage(
      name: AppRoutes.profile,
      page: () => const ProfileScreen(),
      transition: Transition.rightToLeft,
    ),
    
    // Shop
    GetPage(
      name: AppRoutes.shop,
      page: () => const ShopScreen(),
      transition: Transition.rightToLeft,
    ),
    
    // Settings
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsScreen(),
      transition: Transition.rightToLeft,
    ),
  ];
}
