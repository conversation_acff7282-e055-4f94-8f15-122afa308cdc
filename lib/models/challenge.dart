enum ChallengeType {
  tapTimer,
  memoryMatch,
  colorSequence,
  mathQuiz,
}

enum ChallengeDifficulty {
  easy,
  medium,
  hard,
}

class Challenge {
  final String id;
  final String title;
  final String description;
  final ChallengeType type;
  final ChallengeDifficulty difficulty;
  final int coinReward;
  final int timeLimit; // in seconds
  final Map<String, dynamic> gameConfig;

  Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.coinReward,
    required this.timeLimit,
    this.gameConfig = const {},
  });

  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: ChallengeType.values.firstWhere(
        (e) => e.toString() == 'ChallengeType.${json['type']}',
      ),
      difficulty: ChallengeDifficulty.values.firstWhere(
        (e) => e.toString() == 'ChallengeDifficulty.${json['difficulty']}',
      ),
      coinReward: json['coinReward'],
      timeLimit: json['timeLimit'],
      gameConfig: Map<String, dynamic>.from(json['gameConfig'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'difficulty': difficulty.toString().split('.').last,
      'coinReward': coinReward,
      'timeLimit': timeLimit,
      'gameConfig': gameConfig,
    };
  }
}

class ChallengeResult {
  final String challengeId;
  final String userId;
  final int score;
  final int timeSpent; // in seconds
  final bool completed;
  final DateTime completedAt;
  final int coinsEarned;

  ChallengeResult({
    required this.challengeId,
    required this.userId,
    required this.score,
    required this.timeSpent,
    required this.completed,
    required this.completedAt,
    required this.coinsEarned,
  });

  factory ChallengeResult.fromJson(Map<String, dynamic> json) {
    return ChallengeResult(
      challengeId: json['challengeId'],
      userId: json['userId'],
      score: json['score'],
      timeSpent: json['timeSpent'],
      completed: json['completed'],
      completedAt: DateTime.parse(json['completedAt']),
      coinsEarned: json['coinsEarned'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'userId': userId,
      'score': score,
      'timeSpent': timeSpent,
      'completed': completed,
      'completedAt': completedAt.toIso8601String(),
      'coinsEarned': coinsEarned,
    };
  }
}
