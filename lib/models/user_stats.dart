class UserStats {
  final String userId;
  final int totalCoins;
  final int currentStreak;
  final int longestStreak;
  final int totalChallengesCompleted;
  final int totalMeditationMinutes;
  final DateTime lastActivityDate;
  final List<String> achievements;
  final Map<String, int> gameScores; // gameType -> best score

  UserStats({
    required this.userId,
    this.totalCoins = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.totalChallengesCompleted = 0,
    this.totalMeditationMinutes = 0,
    required this.lastActivityDate,
    this.achievements = const [],
    this.gameScores = const {},
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      userId: json['userId'],
      totalCoins: json['totalCoins'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      totalChallengesCompleted: json['totalChallengesCompleted'] ?? 0,
      totalMeditationMinutes: json['totalMeditationMinutes'] ?? 0,
      lastActivityDate: DateTime.parse(json['lastActivityDate']),
      achievements: List<String>.from(json['achievements'] ?? []),
      gameScores: Map<String, int>.from(json['gameScores'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalCoins': totalCoins,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalChallengesCompleted': totalChallengesCompleted,
      'totalMeditationMinutes': totalMeditationMinutes,
      'lastActivityDate': lastActivityDate.toIso8601String(),
      'achievements': achievements,
      'gameScores': gameScores,
    };
  }

  UserStats copyWith({
    String? userId,
    int? totalCoins,
    int? currentStreak,
    int? longestStreak,
    int? totalChallengesCompleted,
    int? totalMeditationMinutes,
    DateTime? lastActivityDate,
    List<String>? achievements,
    Map<String, int>? gameScores,
  }) {
    return UserStats(
      userId: userId ?? this.userId,
      totalCoins: totalCoins ?? this.totalCoins,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      totalChallengesCompleted: totalChallengesCompleted ?? this.totalChallengesCompleted,
      totalMeditationMinutes: totalMeditationMinutes ?? this.totalMeditationMinutes,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      achievements: achievements ?? this.achievements,
      gameScores: gameScores ?? this.gameScores,
    );
  }
}
