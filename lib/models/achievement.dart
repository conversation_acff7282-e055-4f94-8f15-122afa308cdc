enum AchievementType {
  streak,
  challenge,
  meditation,
  coins,
  general,
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final AchievementType type;
  final String? iconPath;
  final int coinReward;
  final Map<String, dynamic> requirements;
  final bool isHidden;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.iconPath,
    required this.coinReward,
    this.requirements = const {},
    this.isHidden = false,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: AchievementType.values.firstWhere(
        (e) => e.toString() == 'AchievementType.${json['type']}',
      ),
      iconPath: json['iconPath'],
      coinReward: json['coinReward'],
      requirements: Map<String, dynamic>.from(json['requirements'] ?? {}),
      isHidden: json['isHidden'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'iconPath': iconPath,
      'coinReward': coinReward,
      'requirements': requirements,
      'isHidden': isHidden,
    };
  }
}

class UserAchievement {
  final String id;
  final String achievementId;
  final String userId;
  final DateTime unlockedAt;
  final int progress;
  final bool isCompleted;

  UserAchievement({
    required this.id,
    required this.achievementId,
    required this.userId,
    required this.unlockedAt,
    this.progress = 0,
    this.isCompleted = false,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) {
    return UserAchievement(
      id: json['id'],
      achievementId: json['achievementId'],
      userId: json['userId'],
      unlockedAt: DateTime.parse(json['unlockedAt']),
      progress: json['progress'] ?? 0,
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'achievementId': achievementId,
      'userId': userId,
      'unlockedAt': unlockedAt.toIso8601String(),
      'progress': progress,
      'isCompleted': isCompleted,
    };
  }
}
