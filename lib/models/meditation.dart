enum MeditationType {
  breathing,
  mindfulness,
  relaxation,
  focus,
}

class Meditation {
  final String id;
  final String title;
  final String description;
  final MeditationType type;
  final int duration; // in minutes
  final String audioPath;
  final String? imagePath;
  final List<String> tags;

  Meditation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.duration,
    required this.audioPath,
    this.imagePath,
    this.tags = const [],
  });

  factory Meditation.fromJson(Map<String, dynamic> json) {
    return Meditation(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: MeditationType.values.firstWhere(
        (e) => e.toString() == 'MeditationType.${json['type']}',
      ),
      duration: json['duration'],
      audioPath: json['audioPath'],
      imagePath: json['imagePath'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'duration': duration,
      'audioPath': audioPath,
      'imagePath': imagePath,
      'tags': tags,
    };
  }
}

class MeditationSession {
  final String id;
  final String meditationId;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final int duration; // actual duration in seconds
  final bool completed;

  MeditationSession({
    required this.id,
    required this.meditationId,
    required this.userId,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.completed,
  });

  factory MeditationSession.fromJson(Map<String, dynamic> json) {
    return MeditationSession(
      id: json['id'],
      meditationId: json['meditationId'],
      userId: json['userId'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      duration: json['duration'],
      completed: json['completed'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'meditationId': meditationId,
      'userId': userId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration,
      'completed': completed,
    };
  }
}
