enum ShopItemType {
  avatar,
  theme,
  powerup,
  decoration,
}

enum ShopItemRarity {
  common,
  rare,
  epic,
  legendary,
}

class ShopItem {
  final String id;
  final String name;
  final String description;
  final ShopItemType type;
  final ShopItemRarity rarity;
  final int price;
  final String? imagePath;
  final Map<String, dynamic> properties;
  final bool isLimited;
  final DateTime? availableUntil;

  ShopItem({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.rarity,
    required this.price,
    this.imagePath,
    this.properties = const {},
    this.isLimited = false,
    this.availableUntil,
  });

  factory ShopItem.fromJson(Map<String, dynamic> json) {
    return ShopItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: ShopItemType.values.firstWhere(
        (e) => e.toString() == 'ShopItemType.${json['type']}',
      ),
      rarity: ShopItemRarity.values.firstWhere(
        (e) => e.toString() == 'ShopItemRarity.${json['rarity']}',
      ),
      price: json['price'],
      imagePath: json['imagePath'],
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
      isLimited: json['isLimited'] ?? false,
      availableUntil: json['availableUntil'] != null 
          ? DateTime.parse(json['availableUntil']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'rarity': rarity.toString().split('.').last,
      'price': price,
      'imagePath': imagePath,
      'properties': properties,
      'isLimited': isLimited,
      'availableUntil': availableUntil?.toIso8601String(),
    };
  }
}

class PurchasedItem {
  final String id;
  final String itemId;
  final String userId;
  final DateTime purchasedAt;
  final int pricePaid;
  final bool isActive;

  PurchasedItem({
    required this.id,
    required this.itemId,
    required this.userId,
    required this.purchasedAt,
    required this.pricePaid,
    this.isActive = false,
  });

  factory PurchasedItem.fromJson(Map<String, dynamic> json) {
    return PurchasedItem(
      id: json['id'],
      itemId: json['itemId'],
      userId: json['userId'],
      purchasedAt: DateTime.parse(json['purchasedAt']),
      pricePaid: json['pricePaid'],
      isActive: json['isActive'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'userId': userId,
      'purchasedAt': purchasedAt.toIso8601String(),
      'pricePaid': pricePaid,
      'isActive': isActive,
    };
  }
}
