import 'package:flutter/material.dart';

class ChallengeScreen extends StatelessWidget {
  const ChallengeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Challenges'),
      ),
      body: const Center(
        child: Text('Challenge Screen - Coming Soon'),
      ),
    );
  }
}

class ChallengeGameScreen extends StatelessWidget {
  const ChallengeGameScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Challenge Game'),
      ),
      body: const Center(
        child: Text('Challenge Game Screen - Coming Soon'),
      ),
    );
  }
}

class ChallengeResultScreen extends StatelessWidget {
  const ChallengeResultScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Challenge Result'),
      ),
      body: const Center(
        child: Text('Challenge Result Screen - Coming Soon'),
      ),
    );
  }
}
